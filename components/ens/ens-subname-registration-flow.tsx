'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount, useSendTransaction, useWaitForTransactionReceipt, useWriteContract } from 'wagmi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectOption } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import {
  CheckCircleIcon,
  ArrowRightIcon,
  WalletIcon,
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  AlertCircleIcon,
  DatabaseIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SettingsIcon
} from 'lucide-react';
import { getENSTokenId, namehash } from '@/lib/ens-utils';
import { SupportedChain, SUPPORTED_CHAINS } from '@/lib/types/ens';
import { FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI } from '@/lib/contracts/factory-contract';

interface ENSSubnameRegistrationFlowProps {
  selectedApplication: ApplicationWithApiKey;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  className?: string;
}

type FlowStep = 'create-registrar' | 'transfer' | 'register' | 'display' | 'complete';

interface FlowState {
  currentStep: FlowStep;
  ensRoot: string;
  contractAddress: string;
  chain: SupportedChain;
  isLoading: boolean;
  error?: string;

  // Step completion tracking
  registrarCreated: boolean;
  createdContractAddress?: string;
  transferCompleted: boolean;
  registrationCompleted: boolean;
  dataFetched: boolean;

  // Transaction data
  registrarTxHash?: string;
  transferTxHash?: string;
  registrationData?: any;
  fetchedData?: any;
}

const STEP_CONFIG = {
  'create-registrar': {
    title: 'Create Subname Registrar',
    description: 'Create a subname registrar contract for your ENS domain',
    icon: SettingsIcon,
    stepNumber: 1
  },
  transfer: {
    title: 'Transfer Ownership to Contract',
    description: 'Transfer ENS ownership from your wallet to the registrar contract',
    icon: ArrowUpDownIcon,
    stepNumber: 2
  },
  register: {
    title: 'Register ENS Root',
    description: 'Store the registration data in the Crefy Connect backend',
    icon: DatabaseIcon,
    stepNumber: 3
  },
  display: {
    title: 'View Registration Data',
    description: 'Retrieve and display your registered ENS data',
    icon: GlobeIcon,
    stepNumber: 4
  }
} as const;

export function ENSSubnameRegistrationFlow({ 
  selectedApplication, 
  onSuccess, 
  onCancel, 
  className = "" 
}: ENSSubnameRegistrationFlowProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [state, setState] = useState<FlowState>({
    currentStep: 'create-registrar',
    ensRoot: '',
    contractAddress: '',
    chain: 'sepolia',
    isLoading: false,
    registrarCreated: false,
    transferCompleted: false,
    registrationCompleted: false,
    dataFetched: false
  });

  // Contract write hook for registrar creation
  const {
    writeContract: writeFactoryContract,
    data: registrarTxHash,
    error: registrarTxError,
    isPending: isRegistrarPending
  } = useWriteContract();

  const {
    isLoading: isRegistrarWaiting,
    isSuccess: isRegistrarSuccess,
    isError: isRegistrarError
  } = useWaitForTransactionReceipt({
    hash: registrarTxHash,
  });

  // Transaction hooks for transfer
  const {
    sendTransaction: sendTransferTx,
    data: transferTxHash,
    error: transferTxError,
    isPending: isTransferPending
  } = useSendTransaction();

  const {
    isLoading: isTransferWaiting,
    isSuccess: isTransferSuccess,
    isError: isTransferError
  } = useWaitForTransactionReceipt({
    hash: transferTxHash,
  });

  // Handle registrar transaction success
  useEffect(() => {
    if (isRegistrarSuccess && registrarTxHash) {
      // For now, we'll set a placeholder contract address
      // In a real implementation, you'd parse the transaction receipt to get the actual contract address
      const placeholderContractAddress = `0x${Math.random().toString(16).substring(2, 42)}`;

      setState(prev => ({
        ...prev,
        registrarTxHash: registrarTxHash,
        registrarCreated: true,
        createdContractAddress: placeholderContractAddress,
        currentStep: 'transfer',
        isLoading: false
      }));
      showToast({
        type: 'success',
        title: 'Registrar Created',
        description: 'Subname registrar contract created successfully'
      });
    }
  }, [isRegistrarSuccess, registrarTxHash, showToast]);

  // Handle registrar transaction errors
  useEffect(() => {
    if (isRegistrarError || registrarTxError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: registrarTxError?.message || 'Registrar creation failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: registrarTxError?.message || 'Registrar creation failed'
      });
    }
  }, [isRegistrarError, registrarTxError, showToast]);

  // Handle transfer transaction success
  useEffect(() => {
    if (isTransferSuccess && transferTxHash) {
      setState(prev => ({
        ...prev,
        transferTxHash: transferTxHash,
        transferCompleted: true,
        currentStep: 'register',
        isLoading: false
      }));
      showToast({
        type: 'success',
        title: 'Transfer Complete',
        description: 'ENS ownership transferred to contract successfully'
      });
    }
  }, [isTransferSuccess, transferTxHash, showToast]);

  // Handle transfer transaction errors
  useEffect(() => {
    if (isTransferError || transferTxError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: transferTxError?.message || 'Transfer transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: transferTxError?.message || 'Transfer transaction failed'
      });
    }
  }, [isTransferError, transferTxError, showToast]);

  const validateENSRoot = (ensRoot: string): string | null => {
    if (!ensRoot) return 'ENS root is required';
    if (!ensRoot.endsWith('.eth')) return 'ENS root must end with .eth';
    return null;
  };

  // Step 1: Create subname registrar contract
  const handleCreateRegistrar = useCallback(async () => {
    if (!state.ensRoot || !isConnected) return;

    const ensError = validateENSRoot(state.ensRoot);
    if (ensError) {
      showToast({
        type: 'error',
        title: 'Invalid ENS Name',
        description: ensError
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Creating registrar contract ===');
      console.log('ENS Name:', state.ensRoot);
      console.log('Chain:', state.chain);

      showToast({
        type: 'info',
        title: 'Creating Contract',
        description: 'Please sign the transaction to create the registrar contract'
      });

      // Calculate the namehash for the ENS domain
      const parentNode = namehash(state.ensRoot.toLowerCase());
      console.log('Parent node (namehash):', parentNode);

      // Call the factory contract directly
      writeFactoryContract({
        address: FACTORY_CONTRACT_ADDRESS as `0x${string}`,
        abi: FACTORY_CONTRACT_ABI,
        functionName: 'createSubnameRegistrar',
        args: [parentNode as `0x${string}`],
        chain: undefined,
        account: undefined,
      } as any);

    } catch (error: any) {
      console.error('Registrar creation failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to create registrar'
      }));
      showToast({
        type: 'error',
        title: 'Creation Failed',
        description: error.message || 'Failed to create registrar contract'
      });
    }
  }, [state.ensRoot, state.chain, isConnected, writeFactoryContract, showToast]);

  // Step 2: Transfer ownership to contract
  const handleTransferOwnership = useCallback(async () => {
    if (!token || !state.ensRoot || !isConnected || !address || !state.createdContractAddress) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Get the token ID for the ENS name
      const tokenId = getENSTokenId(state.ensRoot);

      // Prepare the NameWrapper transfer transaction
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: state.chain,
          from: address,
          to: state.createdContractAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare transfer transaction');
      }

      const txData = response.data.data;

      // Execute transaction using wagmi
      sendTransferTx({
        to: txData.to as `0x${string}`,
        data: txData.data as `0x${string}`,
        value: BigInt(txData.value || '0'),
      });

    } catch (error: any) {
      console.error('Transfer preparation failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to prepare transfer'
      }));
      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: error.message || 'Failed to prepare transfer'
      });
    }
  }, [token, state.ensRoot, state.chain, state.createdContractAddress, isConnected, address, selectedApplication.appId, sendTransferTx, showToast]);

  // Step 3: Register ENS root
  const handleRegisterENSRoot = useCallback(async () => {
    if (!token || !state.ensRoot || !state.createdContractAddress) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiService.registerENSRoot(
        {
          ens_name: state.ensRoot,
          contractAddress: state.createdContractAddress,
          chain: state.chain,
          isActive: true
        },
        token,
        selectedApplication.appId
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to register ENS root');
      }

      setState(prev => ({
        ...prev,
        registrationCompleted: true,
        registrationData: response.data,
        isLoading: false
      }));

      showToast({
        type: 'success',
        title: 'Registration Complete!',
        description: `${state.ensRoot} has been successfully registered`
      });

    } catch (error: any) {
      console.error('ENS registration failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to register ENS root'
      }));
      showToast({
        type: 'error',
        title: 'Registration Failed',
        description: error.message || 'Failed to register ENS root'
      });
    }
  }, [token, state.ensRoot, state.createdContractAddress, state.chain, selectedApplication.appId, showToast]);

  // Step 4: Fetch registration data
  const handleFetchRegistrationData = useCallback(async () => {
    if (!token) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiService.getENSName(token, selectedApplication.appId);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch registration data');
      }

      setState(prev => ({
        ...prev,
        dataFetched: true,
        fetchedData: response.data,
        isLoading: false
      }));

      showToast({
        type: 'success',
        title: 'Data Retrieved!',
        description: 'Registration data fetched successfully'
      });

    } catch (error: any) {
      console.error('Data fetch failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to fetch registration data'
      }));
      showToast({
        type: 'error',
        title: 'Fetch Failed',
        description: error.message || 'Failed to fetch registration data'
      });
    }
  }, [token, selectedApplication.appId, showToast]);

  // Navigation functions
  const goToNextStep = () => {
    const steps: FlowStep[] = ['create-registrar', 'transfer', 'register', 'display'];
    const currentIndex = steps.indexOf(state.currentStep);
    if (currentIndex < steps.length - 1) {
      setState(prev => ({ ...prev, currentStep: steps[currentIndex + 1] }));
    }
  };

  const goToPreviousStep = () => {
    const steps: FlowStep[] = ['create-registrar', 'transfer', 'register', 'display'];
    const currentIndex = steps.indexOf(state.currentStep);
    if (currentIndex > 0) {
      setState(prev => ({ ...prev, currentStep: steps[currentIndex - 1] }));
    }
  };

  const canProceedToNext = () => {
    switch (state.currentStep) {
      case 'create-registrar':
        return state.registrarCreated;
      case 'transfer':
        return state.transferCompleted;
      case 'register':
        return state.registrationCompleted;
      case 'display':
        return state.dataFetched;
      default:
        return false;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Breadcrumb */}
      <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              ENS Subname Registration Flow
            </h2>
            {onCancel && (
              <Button
                onClick={onCancel}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-600 hover:bg-gray-100"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Progress Steps */}
          <div className="flex items-center space-x-4 overflow-x-auto">
            {Object.entries(STEP_CONFIG).map(([stepKey, config], index) => {
              const step = stepKey as FlowStep;
              const isActive = state.currentStep === step;
              const isCompleted =
                (step === 'create-registrar' && state.registrarCreated) ||
                (step === 'transfer' && state.transferCompleted) ||
                (step === 'register' && state.registrationCompleted) ||
                (step === 'display' && state.dataFetched);

              return (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                    isActive
                      ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                      : isCompleted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                  }`}>
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      isActive ? 'bg-white text-[#4A148C]' : ''
                    }`}>
                      {isCompleted ? (
                        <CheckCircleIcon className="w-4 h-4" />
                      ) : (
                        config.stepNumber
                      )}
                    </div>
                    <span className="text-sm font-medium whitespace-nowrap">
                      {config.title}
                    </span>
                  </div>
                  {index < Object.keys(STEP_CONFIG).length - 1 && (
                    <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-2" />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {state.currentStep === 'create-registrar' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <SettingsIcon className="h-5 w-5" />
              Step 1: Create Subname Registrar Contract
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Enter your ENS domain name to create a subname registrar contract that will allow users to claim subnames.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="ensroot" className="text-sm font-medium text-gray-700">
                  ENS Domain Name *
                </Label>
                <Input
                  id="ensroot"
                  placeholder="myproject.eth"
                  value={state.ensRoot}
                  onChange={(e) => setState(prev => ({ ...prev, ensRoot: e.target.value.toLowerCase() }))}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Your registered ENS domain (must end with .eth)
                </p>
              </div>

              <div>
                <Label htmlFor="chain" className="text-sm font-medium text-gray-700">
                  Network *
                </Label>
                <Select
                  value={state.chain}
                  onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value as SupportedChain }))}
                  className="mt-1"
                >
                  {SUPPORTED_CHAINS.map((chain) => (
                    <SelectOption key={chain} value={chain}>
                      {chain === 'sepolia' ? 'Sepolia Testnet' : 'Ethereum Mainnet'}
                    </SelectOption>
                  ))}
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  Select the network where your ENS domain is registered
                </p>
              </div>
            </div>

            {state.ensRoot && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <WalletIcon className="h-5 w-5 text-blue-600" />
                  <p className="font-medium text-blue-800">Registrar Contract Details</p>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">ENS Domain:</span>
                    <span className="font-mono font-medium">{state.ensRoot}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Network:</span>
                    <span className="capitalize">{state.chain}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Connected Wallet:</span>
                    <span className="font-mono text-xs">{address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}</span>
                  </div>
                </div>
                <p className="text-xs text-blue-600 mt-2">
                  This will create a contract that allows users to claim subnames under {state.ensRoot}
                </p>
              </div>
            )}

            {state.registrarCreated && state.createdContractAddress && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <p className="font-medium text-green-800">Registrar Created Successfully!</p>
                </div>
                <div className="text-sm text-green-700 mt-2">
                  <p><strong>Contract Address:</strong></p>
                  <code className="block bg-green-100 p-2 rounded text-xs break-all mt-1">
                    {state.createdContractAddress}
                  </code>
                  <p className="mt-2">You can now proceed to transfer your ENS ownership to this contract.</p>
                </div>
              </div>
            )}

            {!isConnected && (
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-amber-600" />
                  <p className="font-medium text-amber-800">Wallet Connection Required</p>
                </div>
                <p className="text-sm text-amber-600 mt-1">
                  Please connect your wallet to create the registrar contract. You must own the ENS domain you want to create a registrar for.
                </p>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Error</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={onCancel}
                variant="outline"
                disabled={state.isLoading}
              >
                Cancel
              </Button>

              <div className="flex gap-2">
                {!state.registrarCreated && (
                  <Button
                    onClick={handleCreateRegistrar}
                    disabled={!state.ensRoot || state.isLoading || !isConnected}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    {state.isLoading ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Creating Contract...
                      </>
                    ) : isRegistrarPending ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Waiting for Signature...
                      </>
                    ) : isRegistrarWaiting ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Processing Transaction...
                      </>
                    ) : (
                      <>
                        <SettingsIcon className="mr-2 h-4 w-4" />
                        Create Registrar Contract
                      </>
                    )}
                  </Button>
                )}

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Transfer Ownership */}
      {state.currentStep === 'transfer' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <ArrowUpDownIcon className="h-5 w-5" />
              Step 2: Transfer Ownership to Contract
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Transfer ENS ownership from your wallet to the contract address using NameWrapper.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <WalletIcon className="h-5 w-5 text-blue-600" />
                <p className="font-medium text-blue-800">Transfer Details</p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ENS Domain:</span>
                  <span className="font-mono font-medium">{state.ensRoot}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">From:</span>
                  <span className="font-mono text-xs">{address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">To:</span>
                  <span className="font-mono text-xs">{state.createdContractAddress}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Network:</span>
                  <span className="capitalize">{state.chain}</span>
                </div>
              </div>
            </div>

            {state.transferCompleted && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <p className="font-medium text-green-800">Transfer Completed!</p>
                </div>
                <p className="text-sm text-green-600 mt-1">
                  ENS ownership has been successfully transferred to the contract.
                </p>
                {state.transferTxHash && (
                  <p className="text-xs text-green-600 mt-2 font-mono">
                    Transaction: {state.transferTxHash}
                  </p>
                )}
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Transfer Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              <div className="flex gap-2">
                {!state.transferCompleted && (
                  <Button
                    onClick={handleTransferOwnership}
                    disabled={state.isLoading || !isConnected || !state.createdContractAddress}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    {state.isLoading ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Transferring...
                      </>
                    ) : isTransferPending ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Waiting for Signature...
                      </>
                    ) : isTransferWaiting ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Processing Transaction...
                      </>
                    ) : !state.createdContractAddress ? (
                      'Complete Step 1 First'
                    ) : (
                      <>
                        <ArrowUpDownIcon className="mr-2 h-4 w-4" />
                        Transfer Ownership
                      </>
                    )}
                  </Button>
                )}

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Register ENS Root */}
      {state.currentStep === 'register' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <DatabaseIcon className="h-5 w-5" />
              Step 3: Register ENS Root
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Store the registration data in the Crefy Connect backend.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DatabaseIcon className="h-5 w-5 text-purple-600" />
                <p className="font-medium text-purple-800">Registration Data</p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ENS Name:</span>
                  <span className="font-mono font-medium">{state.ensRoot}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Contract Address:</span>
                  <span className="font-mono text-xs">{state.createdContractAddress}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Chain:</span>
                  <span className="capitalize">{state.chain}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Application:</span>
                  <span>{selectedApplication.name}</span>
                </div>
              </div>
            </div>

            {state.registrationCompleted && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <p className="font-medium text-green-800">Registration Completed!</p>
                </div>
                <p className="text-sm text-green-600 mt-1">
                  ENS root has been successfully registered in the backend.
                </p>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Registration Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              <div className="flex gap-2">
                {!state.registrationCompleted && (
                  <Button
                    onClick={handleRegisterENSRoot}
                    disabled={state.isLoading}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    {state.isLoading ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Registering...
                      </>
                    ) : (
                      <>
                        <DatabaseIcon className="mr-2 h-4 w-4" />
                        Register ENS Root
                      </>
                    )}
                  </Button>
                )}

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Display Registration Data */}
      {state.currentStep === 'display' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <GlobeIcon className="h-5 w-5" />
              Step 4: View Registration Data
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Retrieve and display your registered ENS data.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {state.fetchedData ? (
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    <p className="font-medium text-green-800">Registration Complete!</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">ENS Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">ENS Name:</span>
                          <span className="font-mono font-medium">{state.fetchedData.ensName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Contract Address:</span>
                          <span className="font-mono text-xs">{state.fetchedData.contractAddress}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <Badge variant={state.fetchedData.isActive ? "default" : "secondary"}>
                            {state.fetchedData.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Application</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">App Name:</span>
                          <span>{selectedApplication.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">App ID:</span>
                          <span className="font-mono text-xs">{selectedApplication.appId}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">What's Next?</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Your ENS domain is now registered and active</li>
                    <li>• Users can now claim subnames under {state.ensRoot}</li>
                    <li>• You can manage subnames through the dashboard</li>
                    <li>• Monitor subname activity and usage</li>
                  </ul>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <GlobeIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">Click below to fetch your registration data</p>
                <Button
                  onClick={handleFetchRegistrationData}
                  disabled={state.isLoading}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                >
                  {state.isLoading ? (
                    <>
                      <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                      Fetching...
                    </>
                  ) : (
                    <>
                      <GlobeIcon className="mr-2 h-4 w-4" />
                      Fetch Registration Data
                    </>
                  )}
                </Button>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Fetch Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              {state.dataFetched && (
                <Button
                  onClick={() => onSuccess?.(state.fetchedData)}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                >
                  Complete Registration
                  <CheckCircleIcon className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
