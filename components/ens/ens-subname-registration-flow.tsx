'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount, useSendTransaction, useWaitForTransactionReceipt } from 'wagmi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectOption } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import { 
  CheckCircleIcon, 
  ArrowRightIcon, 
  WalletIcon, 
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  AlertCircleIcon,
  SearchIcon,
  RefreshCwIcon,
  UserPlusIcon,
  DatabaseIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from 'lucide-react';
import { getENSTokenId } from '@/lib/ens-utils';
import { SupportedChain, SUPPORTED_CHAINS } from '@/lib/types/ens';

interface ENSSubnameRegistrationFlowProps {
  selectedApplication: ApplicationWithApiKey;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  className?: string;
}

type FlowStep = 'availability' | 'transfer' | 'register' | 'display' | 'complete';

interface FlowState {
  currentStep: FlowStep;
  subName: string;
  ensRoot: string;
  contractAddress: string;
  chain: SupportedChain;
  isLoading: boolean;
  error?: string;
  
  // Step completion tracking
  availabilityChecked: boolean;
  isAvailable: boolean;
  transferCompleted: boolean;
  registrationCompleted: boolean;
  dataFetched: boolean;
  
  // Transaction data
  transferTxHash?: string;
  registrationData?: any;
  fetchedData?: any;
}

const STEP_CONFIG = {
  availability: {
    title: 'Check Subname Availability',
    description: 'Verify that your desired subname is available for registration',
    icon: SearchIcon,
    stepNumber: 1
  },
  transfer: {
    title: 'Transfer Ownership to Contract',
    description: 'Transfer ENS ownership from your wallet to the contract address',
    icon: ArrowUpDownIcon,
    stepNumber: 2
  },
  register: {
    title: 'Register ENS Root',
    description: 'Store the registration data in the Crefy Connect backend',
    icon: DatabaseIcon,
    stepNumber: 3
  },
  display: {
    title: 'View Registration Data',
    description: 'Retrieve and display your registered ENS data',
    icon: GlobeIcon,
    stepNumber: 4
  }
} as const;

export function ENSSubnameRegistrationFlow({ 
  selectedApplication, 
  onSuccess, 
  onCancel, 
  className = "" 
}: ENSSubnameRegistrationFlowProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [state, setState] = useState<FlowState>({
    currentStep: 'availability',
    subName: '',
    ensRoot: '',
    contractAddress: address || '',
    chain: 'sepolia',
    isLoading: false,
    availabilityChecked: false,
    isAvailable: false,
    transferCompleted: false,
    registrationCompleted: false,
    dataFetched: false
  });

  // Transaction hooks
  const {
    sendTransaction: sendTransferTx,
    data: transferTxHash,
    error: transferTxError,
    isPending: isTransferPending
  } = useSendTransaction();

  const {
    isLoading: isTransferWaiting,
    isSuccess: isTransferSuccess,
    isError: isTransferError
  } = useWaitForTransactionReceipt({
    hash: transferTxHash,
  });

  // Update contract address when wallet changes
  useEffect(() => {
    if (address && !state.contractAddress) {
      setState(prev => ({ ...prev, contractAddress: address }));
    }
  }, [address, state.contractAddress]);

  // Handle transfer transaction success
  useEffect(() => {
    if (isTransferSuccess && transferTxHash) {
      setState(prev => ({
        ...prev,
        transferTxHash: transferTxHash,
        transferCompleted: true,
        currentStep: 'register',
        isLoading: false
      }));
      showToast({
        type: 'success',
        title: 'Transfer Complete',
        description: 'ENS ownership transferred to contract successfully'
      });
    }
  }, [isTransferSuccess, transferTxHash, showToast]);

  // Handle transfer transaction errors
  useEffect(() => {
    if (isTransferError || transferTxError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: transferTxError?.message || 'Transfer transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: transferTxError?.message || 'Transfer transaction failed'
      });
    }
  }, [isTransferError, transferTxError, showToast]);

  const validateSubname = (subName: string): string | null => {
    if (!subName) return 'Subname is required';
    if (subName.length < 3) return 'Subname must be at least 3 characters';
    if (subName.length > 63) return 'Subname must be less than 64 characters';
    if (!/^[a-z0-9-]+$/.test(subName)) return 'Subname can only contain lowercase letters, numbers, and hyphens';
    if (subName.startsWith('-') || subName.endsWith('-')) return 'Subname cannot start or end with a hyphen';
    return null;
  };

  const validateENSRoot = (ensRoot: string): string | null => {
    if (!ensRoot) return 'ENS root is required';
    if (!ensRoot.endsWith('.eth')) return 'ENS root must end with .eth';
    return null;
  };

  // Step 1: Check subname availability
  const handleCheckAvailability = useCallback(async () => {
    if (!token || !state.subName) return;

    const subnameError = validateSubname(state.subName);
    if (subnameError) {
      showToast({
        type: 'error',
        title: 'Invalid Subname',
        description: subnameError
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiService.checkSubnameAvailability(
        state.subName,
        state.chain,
        token,
        selectedApplication.appId
      );

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          availabilityChecked: true,
          isAvailable: response.data!.available,
          isLoading: false
        }));

        if (response.data.available) {
          showToast({
            type: 'success',
            title: 'Available!',
            description: `${state.subName} is available for registration`
          });
        } else {
          showToast({
            type: 'error',
            title: 'Not Available',
            description: `${state.subName} is already taken`
          });
        }
      } else {
        throw new Error(response.error || 'Failed to check availability');
      }
    } catch (error: any) {
      console.error('Availability check error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to check availability'
      }));
      showToast({
        type: 'error',
        title: 'Check Failed',
        description: error.message || 'Failed to check availability'
      });
    }
  }, [token, state.subName, state.chain, selectedApplication.appId, showToast]);

  // Step 2: Transfer ownership to contract
  const handleTransferOwnership = useCallback(async () => {
    if (!token || !state.ensRoot || !isConnected || !address) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Get the token ID for the ENS name
      const tokenId = getENSTokenId(state.ensRoot);

      // Prepare the NameWrapper transfer transaction
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: state.chain,
          from: address,
          to: state.contractAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare transfer transaction');
      }

      const txData = response.data.data;

      // Execute transaction using wagmi
      sendTransferTx({
        to: txData.to as `0x${string}`,
        data: txData.data as `0x${string}`,
        value: BigInt(txData.value || '0'),
      });

    } catch (error: any) {
      console.error('Transfer preparation failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to prepare transfer'
      }));
      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: error.message || 'Failed to prepare transfer'
      });
    }
  }, [token, state.ensRoot, state.chain, state.contractAddress, isConnected, address, selectedApplication.appId, sendTransferTx, showToast]);

  // Step 3: Register ENS root
  const handleRegisterENSRoot = useCallback(async () => {
    if (!token || !state.ensRoot || !state.contractAddress) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiService.registerENSRoot(
        {
          ens_name: state.ensRoot,
          contractAddress: state.contractAddress,
          chain: state.chain,
          isActive: true
        },
        token,
        selectedApplication.appId
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to register ENS root');
      }

      setState(prev => ({
        ...prev,
        registrationCompleted: true,
        registrationData: response.data,
        isLoading: false
      }));

      showToast({
        type: 'success',
        title: 'Registration Complete!',
        description: `${state.ensRoot} has been successfully registered`
      });

    } catch (error: any) {
      console.error('ENS registration failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to register ENS root'
      }));
      showToast({
        type: 'error',
        title: 'Registration Failed',
        description: error.message || 'Failed to register ENS root'
      });
    }
  }, [token, state.ensRoot, state.contractAddress, state.chain, selectedApplication.appId, showToast]);

  // Step 4: Fetch registration data
  const handleFetchRegistrationData = useCallback(async () => {
    if (!token) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const response = await apiService.getENSName(token, selectedApplication.appId);

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch registration data');
      }

      setState(prev => ({
        ...prev,
        dataFetched: true,
        fetchedData: response.data,
        isLoading: false
      }));

      showToast({
        type: 'success',
        title: 'Data Retrieved!',
        description: 'Registration data fetched successfully'
      });

    } catch (error: any) {
      console.error('Data fetch failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to fetch registration data'
      }));
      showToast({
        type: 'error',
        title: 'Fetch Failed',
        description: error.message || 'Failed to fetch registration data'
      });
    }
  }, [token, selectedApplication.appId, showToast]);

  // Navigation functions
  const goToNextStep = () => {
    const steps: FlowStep[] = ['availability', 'transfer', 'register', 'display'];
    const currentIndex = steps.indexOf(state.currentStep);
    if (currentIndex < steps.length - 1) {
      setState(prev => ({ ...prev, currentStep: steps[currentIndex + 1] }));
    }
  };

  const goToPreviousStep = () => {
    const steps: FlowStep[] = ['availability', 'transfer', 'register', 'display'];
    const currentIndex = steps.indexOf(state.currentStep);
    if (currentIndex > 0) {
      setState(prev => ({ ...prev, currentStep: steps[currentIndex - 1] }));
    }
  };

  const canProceedToNext = () => {
    switch (state.currentStep) {
      case 'availability':
        return state.availabilityChecked && state.isAvailable;
      case 'transfer':
        return state.transferCompleted;
      case 'register':
        return state.registrationCompleted;
      case 'display':
        return state.dataFetched;
      default:
        return false;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Breadcrumb */}
      <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              ENS Subname Registration Flow
            </h2>
            {onCancel && (
              <Button
                onClick={onCancel}
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-600 hover:bg-gray-100"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Progress Steps */}
          <div className="flex items-center space-x-4 overflow-x-auto">
            {Object.entries(STEP_CONFIG).map(([stepKey, config], index) => {
              const step = stepKey as FlowStep;
              const isActive = state.currentStep === step;
              const isCompleted =
                (step === 'availability' && state.availabilityChecked && state.isAvailable) ||
                (step === 'transfer' && state.transferCompleted) ||
                (step === 'register' && state.registrationCompleted) ||
                (step === 'display' && state.dataFetched);

              return (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all ${
                    isActive
                      ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                      : isCompleted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                  }`}>
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                      isActive ? 'bg-white text-[#4A148C]' : ''
                    }`}>
                      {isCompleted ? (
                        <CheckCircleIcon className="w-4 h-4" />
                      ) : (
                        config.stepNumber
                      )}
                    </div>
                    <span className="text-sm font-medium whitespace-nowrap">
                      {config.title}
                    </span>
                  </div>
                  {index < Object.keys(STEP_CONFIG).length - 1 && (
                    <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-2" />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {state.currentStep === 'availability' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <SearchIcon className="h-5 w-5" />
              Step 1: Check Subname Availability
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Enter your desired subname and ENS root to check availability.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="subname" className="text-sm font-medium text-gray-700">
                  Subname *
                </Label>
                <Input
                  id="subname"
                  placeholder="myusername"
                  value={state.subName}
                  onChange={(e) => setState(prev => ({ ...prev, subName: e.target.value.toLowerCase() }))}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  3-63 characters, lowercase letters, numbers, and hyphens only
                </p>
              </div>

              <div>
                <Label htmlFor="ensroot" className="text-sm font-medium text-gray-700">
                  ENS Root *
                </Label>
                <Input
                  id="ensroot"
                  placeholder="myproject.eth"
                  value={state.ensRoot}
                  onChange={(e) => setState(prev => ({ ...prev, ensRoot: e.target.value.toLowerCase() }))}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Your registered ENS domain (must end with .eth)
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="chain" className="text-sm font-medium text-gray-700">
                Network *
              </Label>
              <Select
                value={state.chain}
                onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value as SupportedChain }))}
                className="mt-1"
              >
                {SUPPORTED_CHAINS.map((chain) => (
                  <SelectOption key={chain} value={chain}>
                    {chain === 'sepolia' ? 'Sepolia Testnet' : 'Ethereum Mainnet'}
                  </SelectOption>
                ))}
              </Select>
            </div>

            {state.subName && state.ensRoot && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Full subname will be:</p>
                <p className="text-lg font-mono font-bold text-[#4A148C]">
                  {state.subName}.{state.ensRoot}
                </p>
              </div>
            )}

            {state.availabilityChecked && (
              <div className={`p-4 rounded-lg ${state.isAvailable ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex items-center gap-2">
                  {state.isAvailable ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  )}
                  <p className={`font-medium ${state.isAvailable ? 'text-green-800' : 'text-red-800'}`}>
                    {state.isAvailable ? 'Available!' : 'Not Available'}
                  </p>
                </div>
                <p className={`text-sm mt-1 ${state.isAvailable ? 'text-green-600' : 'text-red-600'}`}>
                  {state.isAvailable
                    ? `${state.subName}.${state.ensRoot} is available for registration`
                    : `${state.subName}.${state.ensRoot} is already taken`
                  }
                </p>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Error</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={onCancel}
                variant="outline"
                disabled={state.isLoading}
              >
                Cancel
              </Button>

              <div className="flex gap-2">
                <Button
                  onClick={handleCheckAvailability}
                  disabled={!state.subName || !state.ensRoot || state.isLoading}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                >
                  {state.isLoading ? (
                    <>
                      <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <SearchIcon className="mr-2 h-4 w-4" />
                      Check Availability
                    </>
                  )}
                </Button>

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Transfer Ownership */}
      {state.currentStep === 'transfer' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <ArrowUpDownIcon className="h-5 w-5" />
              Step 2: Transfer Ownership to Contract
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Transfer ENS ownership from your wallet to the contract address using NameWrapper.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <WalletIcon className="h-5 w-5 text-blue-600" />
                <p className="font-medium text-blue-800">Transfer Details</p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ENS Name:</span>
                  <span className="font-mono font-medium">{state.subName}.{state.ensRoot}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">From:</span>
                  <span className="font-mono text-xs">{address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">To:</span>
                  <span className="font-mono text-xs">{state.contractAddress}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Network:</span>
                  <span className="capitalize">{state.chain}</span>
                </div>
              </div>
            </div>

            {state.transferCompleted && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <p className="font-medium text-green-800">Transfer Completed!</p>
                </div>
                <p className="text-sm text-green-600 mt-1">
                  ENS ownership has been successfully transferred to the contract.
                </p>
                {state.transferTxHash && (
                  <p className="text-xs text-green-600 mt-2 font-mono">
                    Transaction: {state.transferTxHash}
                  </p>
                )}
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Transfer Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              <div className="flex gap-2">
                {!state.transferCompleted && (
                  <Button
                    onClick={handleTransferOwnership}
                    disabled={state.isLoading || !isConnected}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    {state.isLoading ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Transferring...
                      </>
                    ) : (
                      <>
                        <ArrowUpDownIcon className="mr-2 h-4 w-4" />
                        Transfer Ownership
                      </>
                    )}
                  </Button>
                )}

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Register ENS Root */}
      {state.currentStep === 'register' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <DatabaseIcon className="h-5 w-5" />
              Step 3: Register ENS Root
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Store the registration data in the Crefy Connect backend.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <DatabaseIcon className="h-5 w-5 text-purple-600" />
                <p className="font-medium text-purple-800">Registration Data</p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">ENS Name:</span>
                  <span className="font-mono font-medium">{state.ensRoot}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Contract Address:</span>
                  <span className="font-mono text-xs">{state.contractAddress}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Chain:</span>
                  <span className="capitalize">{state.chain}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Application:</span>
                  <span>{selectedApplication.name}</span>
                </div>
              </div>
            </div>

            {state.registrationCompleted && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <p className="font-medium text-green-800">Registration Completed!</p>
                </div>
                <p className="text-sm text-green-600 mt-1">
                  ENS root has been successfully registered in the backend.
                </p>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Registration Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              <div className="flex gap-2">
                {!state.registrationCompleted && (
                  <Button
                    onClick={handleRegisterENSRoot}
                    disabled={state.isLoading}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    {state.isLoading ? (
                      <>
                        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                        Registering...
                      </>
                    ) : (
                      <>
                        <DatabaseIcon className="mr-2 h-4 w-4" />
                        Register ENS Root
                      </>
                    )}
                  </Button>
                )}

                {canProceedToNext() && (
                  <Button
                    onClick={goToNextStep}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                  >
                    Next Step
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Display Registration Data */}
      {state.currentStep === 'display' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <GlobeIcon className="h-5 w-5" />
              Step 4: View Registration Data
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Retrieve and display your registered ENS data.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {state.fetchedData ? (
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    <p className="font-medium text-green-800">Registration Complete!</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">ENS Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">ENS Name:</span>
                          <span className="font-mono font-medium">{state.fetchedData.ensName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Contract Address:</span>
                          <span className="font-mono text-xs">{state.fetchedData.contractAddress}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <Badge variant={state.fetchedData.isActive ? "default" : "secondary"}>
                            {state.fetchedData.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Application</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">App Name:</span>
                          <span>{selectedApplication.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">App ID:</span>
                          <span className="font-mono text-xs">{selectedApplication.appId}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">What's Next?</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Your ENS domain is now registered and active</li>
                    <li>• Users can now claim subnames under {state.ensRoot}</li>
                    <li>• You can manage subnames through the dashboard</li>
                    <li>• Monitor subname activity and usage</li>
                  </ul>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <GlobeIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">Click below to fetch your registration data</p>
                <Button
                  onClick={handleFetchRegistrationData}
                  disabled={state.isLoading}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                >
                  {state.isLoading ? (
                    <>
                      <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
                      Fetching...
                    </>
                  ) : (
                    <>
                      <GlobeIcon className="mr-2 h-4 w-4" />
                      Fetch Registration Data
                    </>
                  )}
                </Button>
              </div>
            )}

            {state.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="font-medium text-red-800">Fetch Failed</p>
                </div>
                <p className="text-sm text-red-600 mt-1">{state.error}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                disabled={state.isLoading}
              >
                <ChevronLeftIcon className="mr-2 h-4 w-4" />
                Previous
              </Button>

              {state.dataFetched && (
                <Button
                  onClick={() => onSuccess?.(state.fetchedData)}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                >
                  Complete Registration
                  <CheckCircleIcon className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
